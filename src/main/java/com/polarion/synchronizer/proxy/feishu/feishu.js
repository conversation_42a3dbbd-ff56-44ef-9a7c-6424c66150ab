/**
 * 飞书项目连接器UI组件
 * 定义前端配置界面的JavaScript组件
 */

// 飞书项目代理配置模型
App.FeishuProxyConfiguration = App.ProxyConfiguration.extend({
    resourceProperties: [
        'proxyType',
        'projectKey',
        'workItemTypes',
        'excludeChartType',
        'connection'
    ],
    type: "",
    description: function() {
        var connection = this.get('connection');
        var projectKey = this.get('projectKey');

        if (!connection) {
            return '未配置连接';
        }

        var serverUrl = connection.get ? connection.get('serverUrl') : connection.serverUrl;
        if (!serverUrl) {
            serverUrl = 'https://project.feishu.cn';
        }

        if (!projectKey) {
            return '未指定项目Key，服务器: ' + serverUrl;
        }

        return '项目: ' + projectKey + '，服务器: ' + serverUrl;
    }.property('projectKey', 'connection', 'connection.serverUrl'),
    name: '飞书项目配置'
});

// 飞书项目代理配置视图
App.FeishuProxyConfigurationView = Ember.View.extend({
    classNames: ["rows"],
    template: Ember.Handlebars.compile([
        '<div class="control-group">',
        '  <label class="control-label"><span style="color:red">*</span>项目Key:</label>',
        '  <div class="controls">',
        '    {{view Ember.TextField valueBinding="projectKey" placeholder="输入飞书项目的project_key"}}',
        '    <span class="help-inline">飞书项目的唯一标识符</span>',
        '  </div>',
        '</div>',
        '<div class="control-group">',
        '  <label class="control-label">工作项类型:</label>',
        '  <div class="controls">',
        '    {{view Ember.TextField valueBinding="workItemTypes" placeholder="story,task,bug"}}',
        '    <span class="help-inline">要同步的工作项类型，多个类型用逗号分隔</span>',
        '  </div>',
        '</div>',
        '<div class="control-group">',
        '  <label class="control-label">排除图表类型:</label>',
        '  <div class="controls">',
        '    <label class="checkbox">',
        '      <input type="checkbox" {{bindAttr checked="excludeChartType"}} />',
        '      排除图表类型以优化通用字段计算（推荐开启）',
        '    </label>',
        '  </div>',
        '</div>',

    ].join('\n'))
});

// 飞书项目连接模型
App.FeishuConnection = App.Connection.extend({
    resourceProperties: [
        'id',
        'connectionType',
        'serverUrl',
        'authMode',
        'pluginId',  // App ID (在 plugin 模式下映射到 user 字段存储)
        'userKey',   // User Key (仅在 plugin_access_token 模式下使用)
        'password'   // App Secret 或 Token (加密存储)
    ],

    // 初始化默认值
    init: function() {
        this._super();
        if (!this.get('authMode')) {
            this.set('authMode', 'plugin_access_token');
        }
        if (!this.get('serverUrl')) {
            this.set('serverUrl', 'https://project.feishu.cn');
        }

        // 设置字段映射观察器
        this.setupFieldMapping();
    },

    // 设置字段映射逻辑
    setupFieldMapping: function() {
        var self = this;

        // pluginId 变化时同步到 user 字段
        this.addObserver('pluginId', function() {
            var pluginId = self.get('pluginId');
            if (pluginId && pluginId !== self.get('user')) {
                self.set('user', pluginId);
            }
        });

        // user 字段变化时同步到 pluginId（但要避免循环更新）
        this.addObserver('user', function() {
            var user = self.get('user');
            var pluginId = self.get('pluginId');
            // 只有当 user 不是默认值且与 pluginId 不同时才同步
            if (user && user !== 'feishu-token' && user !== pluginId) {
                self.set('pluginId', user);
            }
        });
    },

    // 重写 set 方法以确保字段映射
    set: function(key, value) {
        var result = this._super(key, value);
        var authMode = this.get('authMode') || 'plugin_access_token';

        // 在 plugin 模式下，pluginId 映射到 user 字段进行存储
        if (key === 'pluginId' && value && authMode === 'plugin_access_token') {
            this._super('user', value);
        }

        return result;
    },

    description: function() {
        var serverUrl = this.get('serverUrl') || 'https://project.feishu.cn';
        return '飞书项目连接 (插件访问凭证): ' + serverUrl;
    }.property('serverUrl')
});

// 飞书项目连接视图
App.FeishuConnectionView = Ember.View.extend({
    classNames: ['rows'],
    template: Ember.Handlebars.compile([
        '<div class="control-group">',
        '  <label class="control-label"><span style="color:red">*</span>Server URL:</label>',
        '  <div class="controls">',
        '    {{view Ember.TextField valueBinding="controller.content.serverUrl" placeholder="https://project.feishu.cn"}}',
        '  </div>',
        '</div>',

        '<div class="control-group">',
        '  <label class="control-label"><span style="color:red">*</span>App ID:</label>',
        '  <div class="controls">',
        '    {{view Ember.TextField valueBinding="controller.content.pluginId" placeholder="cli_a7fbxxxxxxxxxxxx"}}',
        '    <span class="help-inline">飞书项目应用的唯一标识符 (app_id)</span>',
        '  </div>',
        '</div>',
        '<div class="control-group">',
        '  <label class="control-label"><span style="color:red">*</span>App Secret:</label>',
        '  <div class="controls">',
        '    {{view Ember.TextField type="password" valueBinding="controller.content.password" placeholder="输入应用密钥"}}',
        '    <span class="help-inline">应用密钥 (app_secret)，用于获取插件访问凭证</span>',
        '  </div>',
        '</div>',
        '<div class="control-group">',
        '  <label class="control-label">User Key:</label>',
        '  <div class="controls">',
        '    {{view Ember.TextField valueBinding="controller.content.userKey" placeholder="输入用户Key（可选）"}}',
        '    <span class="help-inline">用于指定接口调用的用户标识，可通过双击飞书用户头像获取</span>',
        '  </div>',
        '</div>',


    ].join('\n')),








});

// 飞书项目代理配置控制器
App.FeishuProxyConfigurationController = Ember.ObjectController.extend({
    // 可以在这里添加自定义的控制器逻辑

    // 验证项目Key格式
    validateProjectKey: function() {
        var projectKey = this.get('projectKey');
        if (projectKey && projectKey.trim().length > 0) {
            // 可以添加项目Key格式验证逻辑
            return true;
        }
        return false;
    },

    // 验证字段映射配置格式
    validateFieldMapping: function() {
        var fieldMappingConfig = this.get('fieldMappingConfig');
        if (!fieldMappingConfig || fieldMappingConfig.trim().length === 0) {
            return true; // 空配置是允许的
        }

        try {
            var mappings = fieldMappingConfig.split(',');
            for (var i = 0; i < mappings.length; i++) {
                var mapping = mappings[i].trim();
                var parts = mapping.split(':');
                if (parts.length < 2) {
                    return false;
                }
            }
            return true;
        } catch (e) {
            return false;
        }
    },

    // 获取工作项类型选项
    workItemTypeOptions: [
        { value: 'story', label: 'Story' },
        { value: 'task', label: 'Task' },
        { value: 'bug', label: 'Bug' },
        { value: 'epic', label: 'Epic' }
    ],

    // 获取字段类型选项
    fieldTypeOptions: [
        { value: 'TEXT', label: '文本' },
        { value: 'NUMBER', label: '数字' },
        { value: 'OPTION', label: '选项' },
        { value: 'USER', label: '用户' },
        { value: 'DATE', label: '日期' },
        { value: 'MULTI_OPTION', label: '多选' },
        { value: 'RICH_TEXT', label: '富文本' }
    ]
});

// 飞书项目连接控制器
App.FeishuConnectionController = Ember.ObjectController.extend({

    // 初始化
    init: function() {
        this._super();
        // 延迟初始化，确保content对象已经设置
        Ember.run.next(this, function() {
            if (this.get('content')) {
                this.set('content.authMode', 'plugin_access_token');
                // 确保字段映射设置完成
                if (this.get('content').setupFieldMapping) {
                    this.get('content').setupFieldMapping();
                }
                // 初始化字段映射
                this.initializeFieldMapping();
            }
        });
    },

    // 初始化字段映射
    initializeFieldMapping: function() {
        var content = this.get('content');
        if (content) {
            var pluginId = content.get('pluginId');
            var user = content.get('user');

            // Plugin 模式：pluginId 映射到 user 字段存储
            if (pluginId) {
                content.set('user', pluginId);
            } else if (user) {
                // 如果从存储中读取到 user 值，映射回 pluginId 显示
                content.set('pluginId', user);
            }
        }
    },









    // 观察 content 变化，确保字段映射
    contentChanged: function() {
        Ember.run.next(this, function() {
            this.initializeFieldMapping();
        });
    }.observes('content'),

    // 观察 pluginId 变化，同步到 user 字段
    pluginIdChanged: function() {
        var content = this.get('content');
        if (content) {
            var pluginId = content.get('pluginId');
            if (pluginId && pluginId !== content.get('user')) {
                content.set('user', pluginId);
            }
        }
    }.observes('content.pluginId')
});
