package com.polarion.synchronizer.proxy.feishu;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import com.polarion.synchronizer.model.TransferItem;

/**
 * 简单的工作项实现类
 * 用于在飞书项目连接器中表示工作项数据
 */
public class Item extends TransferItem {
    
    private String id;
    private String title;
    private String description;
    private String type;
    private String status;
    private String priority;
    private String assignee;
    private String reporter;
    private Date createdAt;
    private Date updatedAt;
    private Map<String, Object> customFields;
    
    public Item(String id) {
    	super(id);
        this.customFields = new HashMap<>();
    }
    
    public Item(String id, String title) {
        super(id, false);
        this.id = id;
        this.title = title;
    }
    

    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    

    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    

    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    

    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    

    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    

    public String getPriority() {
        return priority;
    }
    
    public void setPriority(String priority) {
        this.priority = priority;
    }
    

    public String getAssignee() {
        return assignee;
    }
    
    public void setAssignee(String assignee) {
        this.assignee = assignee;
    }
    

    public String getReporter() {
        return reporter;
    }
    
    public void setReporter(String reporter) {
        this.reporter = reporter;
    }
    

    public Date getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }
    

    public Date getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }
    

    @NotNull
    public Map<String, Object> getCustomFields() {
        return customFields;
    }
    
    public void setCustomFields(Map<String, Object> customFields) {
        this.customFields = customFields != null ? customFields : new HashMap<>();
    }
    

    @Nullable
    public Object getCustomField(@NotNull String fieldName) {
        return customFields.get(fieldName);
    }
    

    public void setCustomField(@NotNull String fieldName, @Nullable Object value) {
        if (value != null) {
            customFields.put(fieldName, value);
        } else {
            customFields.remove(fieldName);
        }
    }
    

    public boolean hasCustomField(@NotNull String fieldName) {
        return customFields.containsKey(fieldName);
    }
    

    public void removeCustomField(@NotNull String fieldName) {
        customFields.remove(fieldName);
    }
    

    public String toString() {
        return "Item{" +
                "id='" + id + '\'' +
                ", title='" + title + '\'' +
                ", type='" + type + '\'' +
                ", status='" + status + '\'' +
                ", assignee='" + assignee + '\'' +
                '}';
    }
    

    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        Item item = (Item) o;
        return id != null ? id.equals(item.id) : item.id == null;
    }
    

    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
    
    /**
     * 创建工作项的副本
     */
    public Item copy() {
        Item copy = new Item(this.id);
        copy.title = this.title;
        copy.description = this.description;
        copy.type = this.type;
        copy.status = this.status;
        copy.priority = this.priority;
        copy.assignee = this.assignee;
        copy.reporter = this.reporter;
        copy.createdAt = this.createdAt != null ? new Date(this.createdAt.getTime()) : null;
        copy.updatedAt = this.updatedAt != null ? new Date(this.updatedAt.getTime()) : null;
        copy.customFields = new HashMap<>(this.customFields);
        return copy;
    }
}
