# 飞书项目连接器 (Feishu Project Connector)

这是一个用于连接 Polarion 和飞书项目进行双向数据同步的连接器实现。

## 功能特性

- **双向同步**：支持 Polarion 和飞书项目之间的工作项双向同步
- **灵活配置**：支持多种连接参数和同步选项配置
- **自动UI生成**：基于 Polarion Connectors 框架自动生成配置界面
- **数据转换**：内置工作项数据格式转换器
- **错误处理**：完善的错误处理和日志记录机制

## 核心组件

### 1. 连接配置类
- **FeishuConnection.java**：定义飞书项目连接参数
  - 服务器地址 (serverUrl)
  - 应用ID (pluginId)
  - 应用密钥 (pluginSecret)
  - 用户标识 (userKey，可选)

### 2. 代理配置类
- **FeishuProxyConfiguration.java**：定义同步配置参数
  - 项目Key (projectKey)
  - 工作项类型 (workItemTypes)

### 3. 代理实现类
- **FeishuProxy.java**：实现与飞书项目API的具体交互
- **FeishuConnector.java**：处理HTTP通信和认证
- **FeishuProxyFactory.java**：创建代理实例

### 4. 数据转换器
- **Item.java**：简单的工作项实现类

### 5. UI组件
- **feishu.js**：前端JavaScript组件
- **templates.html**：UI模板定义

### 6. 模块注册
- **GuiceModule.java**：Guice模块，注册连接器到Polarion框架

## 安装和配置

### 1. 部署连接器
将编译后的JAR文件放置到Polarion的plugins目录中：
```
polarion/plugins/com.polarion.synchronizer.proxy.feishu.jar
```

### 2. 配置连接
1. 在Polarion管理界面中进入"同步器"配置
2. 创建新的"飞书项目"连接
3. 填写连接参数：
   - 服务器地址：https://project.feishu.cn
   - 应用ID：从飞书项目开放平台获取
   - 应用密钥：从飞书项目开放平台获取

### 3. 配置同步
1. 创建新的同步配置
2. 选择"飞书项目"作为外部系统
3. 配置同步参数：
   - 项目Key：飞书项目的project_key
   - 工作项类型：要同步的工作项类型（如：story,task,bug）
   - 字段映射：使用标准的Polarion映射界面配置字段对应关系

## API接口说明

### 飞书项目API端点
- **查询工作项**：`POST /open_api/{project_key}/work_item/issue/query`
- **创建工作项**：`POST /open_api/{project_key}/work_item/create`
- **更新工作项**：`PUT /open_api/{project_key}/work_item/{work_item_id}`

### 认证方式
支持两种认证方式：
1. **插件访问凭证**：使用pluginId和pluginSecret，SDK自动管理token
2. **虚拟插件访问凭证**：使用pluginId和pluginSecret，适用于开发调试

## 数据映射

### Polarion → 飞书项目
- ID → work_item_id
- Title → name
- Description → description
- Type → work_item_type_key
- Status → status字段
- Priority → priority字段
- Assignee → assignee字段
- Reporter → reporter字段

### 自定义字段
支持通过field_value_list进行自定义字段的双向映射。

## 扩展开发

### 添加新的数据转换器
可以在GuiceModule中添加新的转换器：
```java
this.install(new TranslatorModule<String>("feishu:rich-text", "rich-text", 
    FeishuToRichTextTranslator.class) {});
```

### 自定义字段映射
使用Polarion标准的映射界面来配置字段映射规则。

### UI定制
可以修改feishu.js和templates.html来定制前端配置界面。

## 注意事项

1. **API限制**：注意飞书项目API的调用频率限制
2. **数据格式**：确保数据格式符合飞书项目API规范
3. **权限配置**：确保应用具有足够的权限访问相关接口
4. **错误处理**：监控日志文件以及时发现和处理同步错误

## 故障排除

### 常见问题
1. **连接失败**：检查网络连接和服务器地址配置
2. **认证失败**：验证应用ID和密钥是否正确
3. **同步失败**：检查项目Key和工作项类型配置
4. **数据格式错误**：查看日志了解具体的数据转换错误

### 日志位置
Polarion日志文件通常位于：
```
polarion/data/logs/polarion.log
```

## 版本兼容性

- **Polarion版本**：3.22.1+
- **Java版本**：Java 11+
- **飞书项目API**：v1.0+

## 许可证

本连接器遵循与Polarion相同的许可证协议。
