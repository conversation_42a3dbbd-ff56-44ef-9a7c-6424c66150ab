package com.polarion.synchronizer.proxy.feishu.translators;

import java.util.Collection;

import javax.inject.Inject;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import com.google.inject.assistedinject.Assisted;
import com.polarion.synchronizer.ILogger;
import com.polarion.synchronizer.ISynchronizationContext;
import com.polarion.synchronizer.mapping.TranslationResult;
import com.polarion.synchronizer.mapping.ValueMapping;
import com.polarion.synchronizer.model.Side;
import com.polarion.synchronizer.spi.translators.TypesafeTranslator;

/**
 * 飞书用户字段到Polarion用户字段的转换器
 * 处理用户ID的双向映射和转换
 */
public class FeishuToPolarionUserTranslator extends TypesafeTranslator<String, String, String> {
    
    @NotNull
    private final ILogger logger;
    
    @NotNull
    private final Collection<ValueMapping> valueMappings;
    
    @NotNull
    private final Side fromSide;

    @Inject
    public FeishuToPolarionUserTranslator(@Assisted Collection<ValueMapping> valueMappings, 
                                         @Assisted Side fromSide,
                                         @NotNull ISynchronizationContext context) {
        super(String.class, String.class);
        this.valueMappings = valueMappings;
        this.fromSide = fromSide;
        this.logger = context.getLogger();
    }

    @Override
    public TranslationResult<String> translateUnidirectionalTypesafe(@Nullable String sourceValue, 
                                                                   @Nullable String targetValue) {
        String mappedValue = convertUser(sourceValue);
        return createUnidirectionalResult(mappedValue, targetValue);
    }

    @Override
    public TranslationResult<String> translateBidirectionalTypesafe(@Nullable String sourceBaseline, 
                                                                  @Nullable String sourceValue, 
                                                                  @Nullable String targetBaseline, 
                                                                  @Nullable String targetValue) {
        String mappedValue = convertUser(sourceValue);
        return createBidirectionalResult(sourceBaseline, sourceValue, mappedValue, targetBaseline, targetValue);
    }

    /**
     * 转换用户字段值
     */
    @Nullable
    private String convertUser(@Nullable String userValue) {
        if (userValue == null || userValue.trim().isEmpty()) {
            return null;
        }

        try {
            // 首先检查是否有直接的值映射配置
            String mappedValue = applyValueMappings(userValue);
            if (mappedValue != null && !mappedValue.equals(userValue)) {
                logger.debug("通过值映射转换用户: " + userValue + " -> " + mappedValue);
                return mappedValue;
            }

            // 根据同步方向进行转换
            if (fromSide == Side.RIGHT) {
                // 飞书 -> Polarion
                return convertFeishuToPolarionUser(userValue);
            } else {
                // Polarion -> 飞书
                return convertPolarionToFeishuUser(userValue);
            }
            
        } catch (Exception e) {
            logger.warn("用户字段转换失败: " + userValue, e);
            return userValue; // 转换失败时返回原值
        }
    }

    /**
     * 应用值映射配置
     */
    @Nullable
    private String applyValueMappings(@NotNull String value) {
        for (ValueMapping mapping : valueMappings) {
            if (fromSide == Side.RIGHT && value.equals(mapping.getRight())) {
                return mapping.getLeft();
            } else if (fromSide == Side.LEFT && value.equals(mapping.getLeft())) {
                return mapping.getRight();
            }
        }
        return null;
    }

    /**
     * 将飞书用户ID转换为Polarion用户ID
     */
    @Nullable
    private String convertFeishuToPolarionUser(@NotNull String feishuUserId) {
        try {
            // TODO: 实现具体的飞书到Polarion用户映射逻辑
            // 可能的实现方式：
            // 1. 通过飞书API获取用户邮箱，然后在Polarion中查找对应用户
            // 2. 维护一个用户映射表
            // 3. 使用用户名规则进行转换
            
            logger.debug("飞书用户转换为Polarion用户: " + feishuUserId);
            
            // 暂时返回原值，实际使用时需要实现具体的映射逻辑
            return feishuUserId;
            
        } catch (Exception e) {
            logger.warn("飞书用户转换失败: " + feishuUserId, e);
            return feishuUserId;
        }
    }

    /**
     * 将Polarion用户ID转换为飞书用户ID
     */
    @Nullable
    private String convertPolarionToFeishuUser(@NotNull String polarionUserId) {
    	return polarionUserId;
    }
}
