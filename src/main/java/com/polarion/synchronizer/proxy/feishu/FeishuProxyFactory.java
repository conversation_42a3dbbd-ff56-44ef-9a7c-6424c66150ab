package com.polarion.synchronizer.proxy.feishu;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import com.polarion.core.util.logging.Logger;
import com.polarion.synchronizer.IProxyConfiguration;
import com.polarion.synchronizer.configuration.IConnection;
import com.polarion.synchronizer.model.IProxy;
import com.polarion.synchronizer.model.IProxyFactory;



/**
 * 飞书项目代理工厂类
 * 负责创建飞书项目代理实例
 */
public class FeishuProxyFactory implements IProxyFactory {
    
    private static final Logger logger = Logger.getLogger(FeishuProxyFactory.class);
    
    @Override
    @NotNull
    public IProxy createProxy(@NotNull IProxyConfiguration configuration) {
        logger.info("创建飞书项目代理实例");
        
        if (!(configuration instanceof FeishuProxyConfiguration)) {
            throw new IllegalArgumentException("配置必须是FeishuProxyConfiguration类型，实际类型: " + 
                    configuration.getClass().getName());
        }
        
        FeishuProxyConfiguration feishuConfig = (FeishuProxyConfiguration) configuration;
        
        // 验证配置
        String configError = feishuConfig.checkConfiguration();
        if (configError != null) {
            throw new IllegalArgumentException("飞书项目配置无效: " + configError);
        }
        
        logger.info("使用配置创建飞书项目代理: 项目Key=" + feishuConfig.getProjectKey() + 
                   ", 服务器=" + feishuConfig.getServerUrl());
        
        return new FeishuProxy(feishuConfig);
    }

	@Override
	@Nullable
	public String checkConnection(@NotNull IConnection connection) {
		if (!(connection instanceof FeishuConnection)) {
			return "连接类型错误，期望FeishuConnection，实际: " + connection.getClass().getName();
		}

		FeishuConnection feishuConnection = (FeishuConnection) connection;

		// 首先检查配置的基本有效性
		String configError = feishuConnection.check();
		if (configError != null) {
			return configError;
		}
		return null;
	}
}
