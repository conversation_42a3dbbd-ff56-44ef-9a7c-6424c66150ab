package com.polarion.synchronizer.proxy.feishu.auth;

import java.util.List;

import com.lark.project.Client;
import com.lark.project.core.request.RequestOptions;
import com.lark.project.core.token.AccessTokenType;
import com.lark.project.service.plugin.builder.GetPluginTokenReq;
import com.lark.project.service.plugin.builder.GetPluginTokenResp;
import com.lark.project.service.user.builder.QueryUserDetailReq;
import com.lark.project.service.user.builder.QueryUserDetailResp;

/**
 * 身份验证校验工具类 plugin_access_token - 插件访问凭证
 */
public class AuthValidator {

	/**
	 * 校验Plugin ID和Secret是否有效 通过尝试获取plugin_access_token来验证
	 *
	 * @param pluginId     插件ID
	 * @param pluginSecret 插件密钥
	 * @return 校验结果
	 */
	public static AuthValidationResult validatePluginCredentials(String pluginId, String pluginSecret) {
		return validatePluginCredentials(pluginId, pluginSecret, null, null);
	}

	/**
	 * 校验Plugin ID和Secret是否有效
	 *
	 * @param pluginId     插件ID
	 * @param pluginSecret 插件密钥
	 * @param baseUrl      自定义API基础URL，为null时使用默认URL
	 * @return 校验结果
	 */
	public static AuthValidationResult validatePluginCredentials(String pluginId, String pluginSecret, String baseUrl) {
		return validatePluginCredentials(pluginId, pluginSecret, null, baseUrl);
	}

	/**
	 * 校验Plugin ID、Secret和User Key组合是否有效
	 * 先获取plugin_access_token，然后尝试调用API验证token+userKey是否可用
	 *
	 * @param pluginId     插件ID
	 * @param pluginSecret 插件密钥
	 * @param userKey      用户标识（可选，如果提供则会验证token+userKey的组合）
	 * @param baseUrl      自定义API基础URL，为null时使用默认URL
	 * @return 校验结果
	 */
	public static AuthValidationResult validatePluginCredentials(String pluginId, String pluginSecret, String userKey,
			String baseUrl) {
		try {
			Client.Builder builder = Client.newBuilder(pluginId, pluginSecret);
			if (baseUrl != null && !baseUrl.isEmpty()) {
				builder.openBaseUrl(baseUrl);
			}
			Client client = builder.build();

			// 尝试获取plugin token来验证凭证
			GetPluginTokenReq req = GetPluginTokenReq.newBuilder().pluginID(pluginId).pluginSecret(pluginSecret)
					.type(AccessTokenType.AccessTokenTypePlugin.getValue())
					.build();

			GetPluginTokenResp resp = client.getPluginService().getPluginToken(req, null);
			if (resp.getErr() != null) {
				if (resp.getErr().getCode() != 0) {
					return AuthValidationResult.failure(AuthValidationResult.ErrorType.INVALID_CREDENTIALS,
							"Plugin凭证无效: " + resp.getErr().getMsg(), resp.getErr().getCode());
				}
			}

			if (resp.getData() == null || resp.getData().getToken() == null) {
				return AuthValidationResult.failure(AuthValidationResult.ErrorType.INVALID_CREDENTIALS,
						"Plugin凭证无效: 未能获取到有效token", -1);
			}

			// 如果提供了userKey，则进一步验证token+userKey的组合是否可用
			if (userKey != null && !userKey.isEmpty()) {
				try {
					QueryUserDetailReq userReq = QueryUserDetailReq.newBuilder().userKeys(List.of(userKey)).build();
					QueryUserDetailResp userResp = client.getUserService().queryUserDetail(userReq,
							RequestOptions.newBuilder().userKey(userKey).build());

					if (userResp.getErrCode()!=0) {
						return AuthValidationResult.failure(AuthValidationResult.ErrorType.INVALID_CREDENTIALS,
								"Plugin凭证有效但User Key无效: " + userResp.getErr().getMsg(), userResp.getErr().getCode());
					}

					return AuthValidationResult.success("Plugin凭证和User Key验证成功", resp.getData().getToken(),
							resp.getData().getExpireTime());
				} catch (Exception userEx) {
					return AuthValidationResult.failure(AuthValidationResult.ErrorType.INVALID_CREDENTIALS,
							"Plugin凭证有效但User Key验证失败: " + userEx.getMessage(), -1);
				}
			}

			return AuthValidationResult.success("Plugin凭证验证成功", resp.getData().getToken(),
					resp.getData().getExpireTime());

		} catch (Exception e) {
			return AuthValidationResult.failure(AuthValidationResult.ErrorType.NETWORK_ERROR,
					"网络连接失败: " + e.getMessage(), -1);
		}
	}
}
